#!/usr/bin/env python3
"""
Esempio di utilizzo dei dati caricati per il manageriale di calcio
"""

import json
from typing import Dict, List

def load_data():
    """Carica tutti i dati dai file .dat"""
    with open('data/nations.dat', 'r', encoding='utf-8') as f:
        nations = json.load(f)
    
    with open('data/leagues.dat', 'r', encoding='utf-8') as f:
        leagues = json.load(f)
    
    with open('data/clubs.dat', 'r', encoding='utf-8') as f:
        clubs = json.load(f)
    
    with open('data/players.dat', 'r', encoding='utf-8') as f:
        players = json.load(f)
    
    return nations, leagues, clubs, players

def create_lookup_tables(nations, leagues, clubs):
    """Crea tabelle di lookup per ID -> nome"""
    nation_lookup = {n['id']: n['name'] for n in nations}
    league_lookup = {l['id']: l['name'] for l in leagues}
    club_lookup = {c['id']: c['name'] for c in clubs}
    
    return nation_lookup, league_lookup, club_lookup

def find_top_players(players, limit=10):
    """Trova i migliori giocatori per overall"""
    sorted_players = sorted(players, key=lambda p: p['overall'], reverse=True)
    return sorted_players[:limit]

def find_players_by_position(players, position):
    """Trova giocatori per posizione"""
    return [p for p in players if p['position'] == position]

def find_players_by_club(players, club_id):
    """Trova giocatori per squadra"""
    return [p for p in players if p['club_id'] == club_id]

def print_player_info(player, nation_lookup, league_lookup, club_lookup):
    """Stampa informazioni dettagliate di un giocatore"""
    nation_name = nation_lookup.get(player['nation_id'], 'Unknown')
    club_name = club_lookup.get(player['club_id'], 'Unknown')
    
    print(f"🏆 {player['name']} ({player['age']} anni)")
    print(f"   📍 {nation_name} | 🏟️  {club_name}")
    print(f"   📊 Overall: {player['overall']} | Posizione: {player['position']}")
    print(f"   ⚡ PAC:{player['pace']} SHO:{player['shooting']} PAS:{player['passing']}")
    print(f"   🎯 DRI:{player['dribbling']} DEF:{player['defending']} PHY:{player['physical']}")
    print()

def main():
    """Funzione principale di esempio"""
    print("🏈 FOOTBALL MANAGER - Esempio di utilizzo dati")
    print("=" * 50)
    
    # Carica dati
    nations, leagues, clubs, players = load_data()
    nation_lookup, league_lookup, club_lookup = create_lookup_tables(nations, leagues, clubs)
    
    print(f"📊 Dati caricati:")
    print(f"   - {len(nations)} nazioni")
    print(f"   - {len(leagues)} campionati")
    print(f"   - {len(clubs)} squadre")
    print(f"   - {len(players)} giocatori")
    print()
    
    # Top 10 giocatori
    print("🌟 TOP 10 GIOCATORI PER OVERALL:")
    print("-" * 40)
    top_players = find_top_players(players, 10)
    for i, player in enumerate(top_players, 1):
        print(f"{i:2d}. {player['name']} - {player['overall']} OVR ({player['position']})")
    print()
    
    # Migliori portieri
    print("🥅 TOP 5 PORTIERI:")
    print("-" * 30)
    goalkeepers = find_players_by_position(players, 'GK')
    top_gk = sorted(goalkeepers, key=lambda p: p['overall'], reverse=True)[:5]
    for i, gk in enumerate(top_gk, 1):
        print(f"{i}. {gk['name']} - {gk['overall']} OVR")
    print()
    
    # Migliori attaccanti
    print("⚽ TOP 5 ATTACCANTI:")
    print("-" * 30)
    strikers = find_players_by_position(players, 'ST')
    top_st = sorted(strikers, key=lambda p: p['overall'], reverse=True)[:5]
    for i, st in enumerate(top_st, 1):
        print(f"{i}. {st['name']} - {st['overall']} OVR")
    print()
    
    # Informazioni dettagliate del miglior giocatore
    if top_players:
        print("🏆 DETTAGLI MIGLIOR GIOCATORE:")
        print("-" * 35)
        print_player_info(top_players[0], nation_lookup, league_lookup, club_lookup)
    
    # Statistiche per campionato
    print("🏆 CAMPIONATI CON PIÙ SQUADRE:")
    print("-" * 35)
    leagues_sorted = sorted(leagues, key=lambda l: l['teams_count'], reverse=True)
    for i, league in enumerate(leagues_sorted[:5], 1):
        nation_name = nation_lookup.get(league['nation_id'], 'Unknown')
        print(f"{i}. {league['name']} ({nation_name}) - {league['teams_count']} squadre")
    print()
    
    # Statistiche per squadra
    print("🏟️  SQUADRE CON PIÙ GIOCATORI:")
    print("-" * 35)
    clubs_sorted = sorted(clubs, key=lambda c: c['players_count'], reverse=True)
    for i, club in enumerate(clubs_sorted[:5], 1):
        league_name = league_lookup.get(club['league_id'], 'Unknown')
        print(f"{i}. {club['name']} ({league_name}) - {club['players_count']} giocatori")

if __name__ == "__main__":
    main()
