import csv

# Leggi manualmente le prime righe per capire la struttura
with open('male_fc_24_players.csv', 'r', encoding='utf-8') as f:
    lines = f.readlines()

print("PRIMA RIGA (headers):")
headers = lines[0].strip().split(',')
for i, header in enumerate(headers):
    print(f'{i}: "{header}"')

print("\nSECONDA RIGA (primo giocatore):")
data = lines[1].strip().split(',')
for i, value in enumerate(data):
    if i < len(headers):
        print(f'{i}: {headers[i]} = "{value}"')

print(f"\nTotale colonne headers: {len(headers)}")
print(f"Totale colonne dati: {len(data)}")

# Trova la colonna league
for i, header in enumerate(headers):
    if 'league' in header.lower():
        print(f"\nTrovata colonna league all'indice {i}: '{header}'")
        if i < len(data):
            print(f"Valore: '{data[i]}'")
