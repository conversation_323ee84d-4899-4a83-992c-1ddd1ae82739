import csv

with open('male_fc_24_players.csv', 'r', encoding='utf-8') as f:
    reader = csv.DictReader(f)
    headers = reader.fieldnames
    print('HEADERS:')
    for i, header in enumerate(headers):
        print(f'{i}: {header}')
    print()
    
    # <PERSON>gg<PERSON> le prime 3 righe per vedere i dati
    for i, row in enumerate(reader):
        if i >= 3:
            break
        print(f'ROW {i+1}:')
        # Mostra le colonne corrette
        print(f'  name: {row.get("name", "N/A")}')
        print(f'  nation: {row.get("nation", "N/A")}')
        print(f'  club: {row.get("club", "N/A")}')
        print(f'  position: {row.get("position", "N/A")}')
        print(f'  preferred foot: {row.get("preferred foot", "N/A")}')
        print(f'  league (pos 47): {list(row.values())[47] if len(list(row.values())) > 47 else "N/A"}')
        print()
