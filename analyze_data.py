#!/usr/bin/env python3
"""
Script per analizzare i dati del dataset FIFA 24
"""
import csv
from collections import defaultdict, Counter

def analyze_fifa_data():
    """Analizza il dataset FIFA 24 e stampa statistiche utili"""
    
    players = []
    clubs = set()
    leagues = set()
    nations = set()
    positions = set()
    ages = []
    overalls = []
    
    print("Caricamento dati FIFA 24...")
    
    with open('male_fc_24_players.csv', 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        
        for row in reader:
            players.append(row)
            clubs.add(row['club'])
            leagues.add(row['league'])
            nations.add(row['nation'])
            positions.add(row['position'])
            
            try:
                ages.append(int(row['age']))
                overalls.append(int(row['overall']))
            except ValueError:
                continue
    
    print(f"\n=== STATISTICHE DATASET FIFA 24 ===")
    print(f"Totale giocatori: {len(players)}")
    print(f"Squadre uniche: {len(clubs)}")
    print(f"Campionati unici: {len(leagues)}")
    print(f"Nazioni uniche: {len(nations)}")
    print(f"Posizioni uniche: {len(positions)}")
    
    if ages:
        print(f"Range età: {min(ages)} - {max(ages)} anni")
    if overalls:
        print(f"Range overall: {min(overalls)} - {max(overalls)}")
    
    print(f"\n=== POSIZIONI ===")
    for pos in sorted(positions):
        print(f"- {pos}")
    
    print(f"\n=== TOP 10 CAMPIONATI (per numero giocatori) ===")
    league_counts = Counter(row['league'] for row in players)
    for league, count in league_counts.most_common(10):
        print(f"- {league}: {count} giocatori")
    
    print(f"\n=== TOP 10 SQUADRE (per numero giocatori) ===")
    club_counts = Counter(row['club'] for row in players)
    for club, count in club_counts.most_common(10):
        print(f"- {club}: {count} giocatori")
    
    return players, clubs, leagues, nations, positions

if __name__ == "__main__":
    analyze_fifa_data()
