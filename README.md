# Football Manager - <PERSON> <PERSON><PERSON> per caricare e strutturare i dati FIFA 24 per un manageriale di calcio.

## 📁 Struttura dei File

Il sistema genera 4 file `.dat` principali:

### 1. `nations.dat` - <PERSON><PERSON>
```json
{
  "id": 1,
  "name": "France",
  "code": "FRA"
}
```

### 2. `leagues.dat` - Campionati
```json
{
  "id": 1,
  "name": "Ligue 1 Uber Eats",
  "nation_id": 1,
  "level": 1,
  "teams_count": 1
}
```

### 3. `clubs.dat` - Squadre
```json
{
  "id": 1,
  "name": "Paris SG",
  "league_id": 1,
  "nation_id": 1,
  "players_count": 348
}
```

### 4. `players.dat` - <PERSON><PERSON><PERSON><PERSON>
```json
{
  "id": 1,
  "name": "<PERSON><PERSON><PERSON>",
  "nation_id": 1,
  "club_id": 1,
  "position": "ST",
  "alternative_positions": "'ST",
  "age": 25,
  "overall": 91,
  "pace": 97,
  "shooting": 90,
  "passing": 80,
  "dribbling": 92,
  "defending": 36,
  "physical": 78,
  "height": "77",
  "weight": "64",
  "preferred_foot": "Right",
  "weak_foot": 0,
  "skill_moves": 0,
  "gk_diving": 0,
  "gk_handling": 5,
  "gk_kicking": null,
  "gk_positioning": null,
  "gk_reflexes": null
}
```

## 🚀 Come Utilizzare

### 1. Caricamento Dati
```bash
python football_manager.py
```

Questo comando:
- Carica i dati dal file `male_fc_24_players.csv`
- Crea le entità (nazioni, campionati, squadre, giocatori) con ID univoci
- Salva tutto nella cartella `data/` in formato JSON

### 2. Utilizzo dei Dati
```python
import json

# Carica i dati
with open('data/players.dat', 'r', encoding='utf-8') as f:
    players = json.load(f)

with open('data/clubs.dat', 'r', encoding='utf-8') as f:
    clubs = json.load(f)

# Trova giocatori per squadra
def find_players_by_club(players, club_id):
    return [p for p in players if p['club_id'] == club_id]

# Trova migliori giocatori
def find_top_players(players, limit=10):
    return sorted(players, key=lambda p: p['overall'], reverse=True)[:limit]
```

### 3. Esempio Completo
```bash
python example_usage.py
```

## 📊 Statistiche Dati Caricati

- **Nazioni**: 24
- **Campionati**: 29  
- **Squadre**: 24
- **Giocatori**: 1740

## 🔧 Struttura delle Classi

### Nation
- `id`: ID univoco
- `name`: Nome della nazione
- `code`: Codice ISO (3 lettere)

### League  
- `id`: ID univoco
- `name`: Nome del campionato
- `nation_id`: ID della nazione
- `level`: Livello (1 = prima divisione)
- `teams_count`: Numero di squadre

### Club
- `id`: ID univoco
- `name`: Nome della squadra
- `league_id`: ID del campionato
- `nation_id`: ID della nazione
- `players_count`: Numero di giocatori

### Player
- `id`: ID univoco
- `name`: Nome del giocatore
- `nation_id`: ID della nazione
- `club_id`: ID della squadra
- `position`: Posizione principale
- `age`: Età
- `overall`: Overall FIFA
- Attributi fisici: `pace`, `shooting`, `passing`, `dribbling`, `defending`, `physical`
- Info fisiche: `height`, `weight`, `preferred_foot`, `weak_foot`, `skill_moves`
- Attributi portiere: `gk_diving`, `gk_handling`, `gk_kicking`, `gk_positioning`, `gk_reflexes`

## 🎮 Utilizzo per Manageriale

I file `.dat` possono essere utilizzati per:

1. **Sistema di Trasferimenti**: Cerca giocatori per posizione, età, overall
2. **Gestione Squadre**: Carica rose complete delle squadre
3. **Campionati**: Organizza squadre per campionato
4. **Statistiche**: Analizza dati per overall, età, nazionalità
5. **Simulazioni**: Usa gli attributi per calcolare performance

## 🔍 Funzioni di Ricerca Utili

```python
# Giocatori per posizione
strikers = [p for p in players if p['position'] == 'ST']

# Giocatori per età
young_players = [p for p in players if p['age'] <= 21]

# Giocatori per overall
top_players = [p for p in players if p['overall'] >= 85]

# Portieri
goalkeepers = [p for p in players if p['position'] == 'GK']
```

## 📝 Note

- I dati sono filtrati per overall >= 40 e età tra 15-50 anni
- Alcuni caratteri speciali sono stati puliti automaticamente
- Gli ID sono sequenziali e univoci per ogni entità
- I file JSON sono in formato UTF-8 per supportare caratteri internazionali
