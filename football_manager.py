#!/usr/bin/env python3
"""
Football Manager - Manageriale di Calcio
Basato sui dati FIFA 24
"""

import csv
import json
import random
from dataclasses import dataclass
from typing import List, Dict, Optional
from enum import Enum

class Position(Enum):
    """Posizioni dei giocatori"""
    GK = "GK"
    CB = "CB"
    LB = "LB"
    RB = "RB"
    CDM = "CDM"
    CM = "CM"
    CAM = "CAM"
    LW = "LW"
    RW = "RW"
    RM = "RM"
    ST = "ST"
    CF = "CF"

@dataclass
class Player:
    """Classe per rappresentare un giocatore"""
    id: int
    name: str
    nation: str
    club: str
    position: str
    alternative_positions: str
    age: int
    overall: int
    
    # Attributi fisici
    pace: int
    shooting: int
    passing: int
    dribbling: int
    defending: int
    physical: int
    
    # Attributi dettagliati
    acceleration: int
    sprint_speed: int
    positioning: int
    finishing: int
    shot_power: int
    long_shots: int
    volleys: int
    penalties: int
    vision: int
    crossing: int
    free_kick_accuracy: int
    short_passing: int
    long_passing: int
    curve: int
    agility: int
    balance: int
    reactions: int
    ball_control: int
    composure: int
    interceptions: int
    heading_accuracy: int
    def_awareness: int
    standing_tackle: int
    sliding_tackle: int
    jumping: int
    stamina: int
    strength: int
    aggression: int
    
    # Info fisiche
    height: str
    weight: str
    preferred_foot: str
    weak_foot: int
    skill_moves: int
    
    # Info carriera
    league: str
    att_work_rate: str
    def_work_rate: str
    
    # Attributi portiere (se applicabile)
    gk_diving: Optional[int] = None
    gk_handling: Optional[int] = None
    gk_kicking: Optional[int] = None
    gk_positioning: Optional[int] = None
    gk_reflexes: Optional[int] = None
    
    def is_goalkeeper(self) -> bool:
        """Controlla se il giocatore è un portiere"""
        return self.position == "GK"
    
    def get_main_attributes(self) -> Dict[str, int]:
        """Restituisce gli attributi principali del giocatore"""
        if self.is_goalkeeper():
            return {
                "diving": self.gk_diving or 0,
                "handling": self.gk_handling or 0,
                "kicking": self.gk_kicking or 0,
                "positioning": self.gk_positioning or 0,
                "reflexes": self.gk_reflexes or 0
            }
        else:
            return {
                "pace": self.pace,
                "shooting": self.shooting,
                "passing": self.passing,
                "dribbling": self.dribbling,
                "defending": self.defending,
                "physical": self.physical
            }
    
    def get_market_value(self) -> int:
        """Calcola il valore di mercato basato su overall e età"""
        base_value = self.overall * 100000
        
        # Fattore età
        if self.age <= 21:
            age_factor = 1.5  # Giovani promettenti
        elif self.age <= 25:
            age_factor = 1.3  # Prime
        elif self.age <= 29:
            age_factor = 1.0  # Maturi
        elif self.age <= 32:
            age_factor = 0.7  # Veterani
        else:
            age_factor = 0.4  # Anziani
        
        return int(base_value * age_factor)

@dataclass
class Team:
    """Classe per rappresentare una squadra"""
    name: str
    league: str
    players: List[Player]
    budget: int = 50000000  # Budget iniziale
    
    def get_starting_eleven(self) -> List[Player]:
        """Restituisce la formazione titolare (4-3-3)"""
        formation = {
            "GK": 1,
            "CB": 2,
            "LB": 1,
            "RB": 1,
            "CM": 2,
            "CAM": 1,
            "LW": 1,
            "RW": 1,
            "ST": 1
        }
        
        starting_eleven = []
        players_by_position = {}
        
        # Raggruppa giocatori per posizione
        for player in self.players:
            pos = player.position
            if pos not in players_by_position:
                players_by_position[pos] = []
            players_by_position[pos].append(player)
        
        # Ordina per overall
        for pos in players_by_position:
            players_by_position[pos].sort(key=lambda p: p.overall, reverse=True)
        
        # Seleziona i migliori per ogni posizione
        for pos, count in formation.items():
            if pos in players_by_position:
                starting_eleven.extend(players_by_position[pos][:count])
        
        return starting_eleven[:11]  # Massimo 11 giocatori
    
    def get_team_overall(self) -> float:
        """Calcola l'overall medio della squadra"""
        starting_eleven = self.get_starting_eleven()
        if not starting_eleven:
            return 0.0
        return sum(player.overall for player in starting_eleven) / len(starting_eleven)
    
    def add_player(self, player: Player) -> bool:
        """Aggiunge un giocatore alla squadra"""
        if self.budget >= player.get_market_value():
            self.players.append(player)
            self.budget -= player.get_market_value()
            player.club = self.name
            return True
        return False
    
    def remove_player(self, player: Player) -> bool:
        """Rimuove un giocatore dalla squadra"""
        if player in self.players:
            self.players.remove(player)
            self.budget += player.get_market_value() // 2  # Vendi a metà prezzo
            return True
        return False

@dataclass
class League:
    """Classe per rappresentare un campionato"""
    name: str
    teams: List[Team]
    current_matchday: int = 1
    
    def get_standings(self) -> List[Dict]:
        """Restituisce la classifica del campionato"""
        standings = []
        for team in self.teams:
            standings.append({
                "team": team.name,
                "overall": team.get_team_overall(),
                "players": len(team.players),
                "budget": team.budget
            })
        
        # Ordina per overall medio
        standings.sort(key=lambda x: x["overall"], reverse=True)
        return standings

class FootballManager:
    """Classe principale del manageriale"""
    
    def __init__(self):
        self.players: List[Player] = []
        self.teams: List[Team] = []
        self.leagues: List[League] = []
        self.user_team: Optional[Team] = None
    
    def load_players_from_csv(self, filename: str) -> None:
        """Carica i giocatori dal file CSV"""
        print(f"Caricamento giocatori da {filename}...")
        
        with open(filename, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            
            for i, row in enumerate(reader):
                try:
                    # Pulisci i dati problematici
                    def safe_int(value, default=0):
                        try:
                            return int(value) if value and value.strip() else default
                        except (ValueError, AttributeError):
                            return default
                    
                    def safe_str(value, default=""):
                        return str(value).strip() if value else default
                    
                    player = Player(
                        id=i,
                        name=safe_str(row.get('name', '')),
                        nation=safe_str(row.get('nation', '')),
                        club=safe_str(row.get('club', '')),
                        position=safe_str(row.get('position', '')),
                        alternative_positions=safe_str(row.get('alternative positions', '')),
                        age=safe_int(row.get('age')),
                        overall=safe_int(row.get('overall')),
                        pace=safe_int(row.get('PAC')),
                        shooting=safe_int(row.get('SHO')),
                        passing=safe_int(row.get('PAS')),
                        dribbling=safe_int(row.get('DRI')),
                        defending=safe_int(row.get('DEF')),
                        physical=safe_int(row.get('PHY')),
                        acceleration=safe_int(row.get('Acceleration')),
                        sprint_speed=safe_int(row.get('Sprint Speed')),
                        positioning=safe_int(row.get('Positioning')),
                        finishing=safe_int(row.get('Finishing')),
                        shot_power=safe_int(row.get('Shot Power')),
                        long_shots=safe_int(row.get('Long Shots')),
                        volleys=safe_int(row.get('Volleys')),
                        penalties=safe_int(row.get('Penalties')),
                        vision=safe_int(row.get('Vision')),
                        crossing=safe_int(row.get('Crossing')),
                        free_kick_accuracy=safe_int(row.get('Free Kick Accuracy')),
                        short_passing=safe_int(row.get('Short Passing')),
                        long_passing=safe_int(row.get('Long Passing')),
                        curve=safe_int(row.get('Curve')),
                        agility=safe_int(row.get('Agility')),
                        balance=safe_int(row.get('Balance')),
                        reactions=safe_int(row.get('Reactions')),
                        ball_control=safe_int(row.get('Ball Control')),
                        composure=safe_int(row.get('Composure')),
                        interceptions=safe_int(row.get('Interceptions')),
                        heading_accuracy=safe_int(row.get('Heading Accuracy')),
                        def_awareness=safe_int(row.get('Def Awareness')),
                        standing_tackle=safe_int(row.get('Standing Tackle')),
                        sliding_tackle=safe_int(row.get('Sliding Tackle')),
                        jumping=safe_int(row.get('Jumping')),
                        stamina=safe_int(row.get('Stamina')),
                        strength=safe_int(row.get('Strength')),
                        aggression=safe_int(row.get('Aggression')),
                        height=safe_str(row.get('height', '')),
                        weight=safe_str(row.get('weight', '')),
                        preferred_foot=safe_str(row.get('preferred foot', '')),
                        weak_foot=safe_int(row.get('weak foot')),
                        skill_moves=safe_int(row.get('skill moves')),
                        league=safe_str(row.get('league', '')),
                        att_work_rate=safe_str(row.get('att work rate', '')),
                        def_work_rate=safe_str(row.get('def work rate', '')),
                        gk_diving=safe_int(row.get('GK Diving')) if row.get('GK Diving') else None,
                        gk_handling=safe_int(row.get('GK Handling')) if row.get('GK Handling') else None,
                        gk_kicking=safe_int(row.get('GK Kicking')) if row.get('GK Kicking') else None,
                        gk_positioning=safe_int(row.get('GK Positioning')) if row.get('GK Positioning') else None,
                        gk_reflexes=safe_int(row.get('GK Reflexes')) if row.get('GK Reflexes') else None
                    )
                    
                    # Filtra giocatori con dati validi
                    if player.name and player.overall > 0 and player.age > 0:
                        self.players.append(player)
                        
                except Exception as e:
                    print(f"Errore nel caricamento del giocatore alla riga {i+2}: {e}")
                    continue
        
        print(f"Caricati {len(self.players)} giocatori validi.")
    
    def create_teams_from_players(self) -> None:
        """Crea le squadre dai giocatori caricati"""
        teams_dict = {}
        
        for player in self.players:
            if player.club not in teams_dict:
                teams_dict[player.club] = Team(
                    name=player.club,
                    league=player.league,
                    players=[]
                )
            teams_dict[player.club].players.append(player)
        
        self.teams = list(teams_dict.values())
        
        # Filtra squadre con almeno 11 giocatori
        self.teams = [team for team in self.teams if len(team.players) >= 11]
        
        print(f"Create {len(self.teams)} squadre con almeno 11 giocatori.")
    
    def create_leagues_from_teams(self) -> None:
        """Crea i campionati dalle squadre"""
        leagues_dict = {}
        
        for team in self.teams:
            if team.league not in leagues_dict:
                leagues_dict[team.league] = League(
                    name=team.league,
                    teams=[]
                )
            leagues_dict[team.league].teams.append(team)
        
        self.leagues = list(leagues_dict.values())
        
        # Filtra campionati con almeno 4 squadre
        self.leagues = [league for league in self.leagues if len(league.teams) >= 4]
        
        print(f"Creati {len(self.leagues)} campionati con almeno 4 squadre.")

if __name__ == "__main__":
    # Test del sistema
    fm = FootballManager()
    fm.load_players_from_csv('male_fc_24_players.csv')
    fm.create_teams_from_players()
    fm.create_leagues_from_teams()
    
    print(f"\n=== STATISTICHE SISTEMA ===")
    print(f"Giocatori caricati: {len(fm.players)}")
    print(f"Squadre create: {len(fm.teams)}")
    print(f"Campionati creati: {len(fm.leagues)}")
    
    if fm.leagues:
        print(f"\n=== CAMPIONATI DISPONIBILI ===")
        for league in fm.leagues[:5]:  # Mostra i primi 5
            print(f"- {league.name}: {len(league.teams)} squadre")
