#!/usr/bin/env python3
"""
Data Loader per Football Manager
<PERSON><PERSON> e struttura i dati FIFA 24 in file .dat
"""

import csv
import json
import os
from dataclasses import dataclass, asdict
from typing import List, Dict, Optional, Set
from collections import defaultdict

@dataclass
class Nation:
    """Classe per rappresentare una nazione"""
    id: int
    name: str
    code: str  # Codice ISO (es. ITA, FRA, etc.)

    def to_dict(self) -> Dict:
        return asdict(self)

@dataclass
class League:
    """Classe per rappresentare un campionato"""
    id: int
    name: str
    nation_id: int
    level: int  # 1 = prima divisione, 2 = seconda, etc.
    teams_count: int = 0

    def to_dict(self) -> Dict:
        return asdict(self)

@dataclass
class Club:
    """Classe per rappresentare una squadra"""
    id: int
    name: str
    league_id: int
    nation_id: int
    players_count: int = 0

    def to_dict(self) -> Dict:
        return asdict(self)

@dataclass
class Player:
    """Classe per rappresentare un giocatore"""
    id: int
    name: str
    nation_id: int
    club_id: int
    position: str
    alternative_positions: str
    age: int
    overall: int

    # Attributi fisici principali
    pace: int
    shooting: int
    passing: int
    dribbling: int
    defending: int
    physical: int

    # Info fisiche
    height: str
    weight: str
    preferred_foot: str
    weak_foot: int
    skill_moves: int

    # Attributi portiere (se applicabile)
    gk_diving: Optional[int] = None
    gk_handling: Optional[int] = None
    gk_kicking: Optional[int] = None
    gk_positioning: Optional[int] = None
    gk_reflexes: Optional[int] = None

    def to_dict(self) -> Dict:
        return asdict(self)

    def is_goalkeeper(self) -> bool:
        """Controlla se il giocatore è un portiere"""
        return self.position == "GK"

class DataLoader:
    """Classe per caricare e strutturare i dati FIFA 24"""

    def __init__(self):
        self.nations: Dict[str, Nation] = {}
        self.leagues: Dict[str, League] = {}
        self.clubs: Dict[str, Club] = {}
        self.players: List[Player] = []

        # Contatori per ID
        self.nation_id_counter = 1
        self.league_id_counter = 1
        self.club_id_counter = 1
        self.player_id_counter = 1

        # Mappature nome -> ID
        self.nation_name_to_id: Dict[str, int] = {}
        self.league_name_to_id: Dict[str, int] = {}
        self.club_name_to_id: Dict[str, int] = {}

    def clean_text(self, text: str) -> str:
        """Pulisce il testo da caratteri problematici"""
        if not text:
            return ""

        # Sostituzioni per caratteri codificati male
        replacements = {
            '+//3//Q-': 'ü',
            '+ACI-': "'",
            '+ACIAIg-': '"',
            'M+//3//Q-nchen': 'München',
            'Atl+//3//Q-tico': 'Atlético',
            'R+//3//Q-ben': 'Rúben',
            'Marc+AC0-Andr+//3//Q-': 'Marc-André'
        }

        cleaned = str(text).strip()
        for old, new in replacements.items():
            cleaned = cleaned.replace(old, new)

        return cleaned

    def safe_int(self, value, default=0) -> int:
        """Converte in int in modo sicuro"""
        try:
            return int(value) if value and str(value).strip() else default
        except (ValueError, TypeError):
            return default

    def get_or_create_nation(self, nation_name: str) -> int:
        """Ottiene o crea una nazione e restituisce l'ID"""
        nation_name = self.clean_text(nation_name)

        if nation_name in self.nation_name_to_id:
            return self.nation_name_to_id[nation_name]

        # Crea nuova nazione
        nation_id = self.nation_id_counter
        self.nation_id_counter += 1

        # Genera codice ISO semplificato (prime 3 lettere maiuscole)
        code = nation_name[:3].upper() if nation_name else "UNK"

        nation = Nation(
            id=nation_id,
            name=nation_name,
            code=code
        )

        self.nations[nation_name] = nation
        self.nation_name_to_id[nation_name] = nation_id

        return nation_id

    def get_or_create_league(self, league_name: str, nation_name: str) -> int:
        """Ottiene o crea un campionato e restituisce l'ID"""
        league_name = self.clean_text(league_name)

        if league_name in self.league_name_to_id:
            return self.league_name_to_id[league_name]

        # Crea nuovo campionato
        league_id = self.league_id_counter
        self.league_id_counter += 1

        nation_id = self.get_or_create_nation(nation_name)

        league = League(
            id=league_id,
            name=league_name,
            nation_id=nation_id,
            level=1  # Assumiamo prima divisione per ora
        )

        self.leagues[league_name] = league
        self.league_name_to_id[league_name] = league_id

        return league_id

    def get_or_create_club(self, club_name: str, league_name: str, nation_name: str) -> int:
        """Ottiene o crea una squadra e restituisce l'ID"""
        club_name = self.clean_text(club_name)

        if club_name in self.club_name_to_id:
            return self.club_name_to_id[club_name]

        # Crea nuova squadra
        club_id = self.club_id_counter
        self.club_id_counter += 1

        league_id = self.get_or_create_league(league_name, nation_name)
        nation_id = self.get_or_create_nation(nation_name)

        club = Club(
            id=club_id,
            name=club_name,
            league_id=league_id,
            nation_id=nation_id
        )

        self.clubs[club_name] = club
        self.club_name_to_id[club_name] = club_id

        return club_id

    def load_players_from_csv(self, filename: str) -> None:
        """Carica i giocatori dal file CSV e crea tutte le entità"""
        print(f"Caricamento dati da {filename}...")

        with open(filename, 'r', encoding='utf-8') as file:
            # Leggi manualmente per gestire il problema delle virgole
            lines = file.readlines()

            for line_num, line in enumerate(lines[1:], 2):  # Salta header
                try:
                    # Split manuale per gestire le virgole nelle posizioni alternative
                    parts = line.strip().split(',')

                    # Estrai dati base usando indici fissi
                    if len(parts) < 50:
                        continue

                    player_name = self.clean_text(parts[1])
                    nation_name = self.clean_text(parts[2])
                    club_name = self.clean_text(parts[3])
                    position = self.clean_text(parts[4])

                    # La colonna league è spostata a causa delle virgole nelle posizioni alternative
                    # Cerchiamo il vero valore della league
                    league_name = ""
                    for i in range(48, min(52, len(parts))):
                        potential_league = self.clean_text(parts[i])
                        # Controlla se sembra un nome di campionato
                        if potential_league and len(potential_league) > 3 and not potential_league.isdigit() and potential_league not in ['High', 'Low', 'Medium', 'Left', 'Right']:
                            league_name = potential_league
                            break

                    # Se non troviamo un campionato valido, usa un default
                    if not league_name:
                        league_name = "Unknown League"

                    # Salta righe con dati mancanti essenziali
                    if not all([player_name, nation_name, club_name]):
                        continue

                    # Ottieni o crea ID per le entità
                    nation_id = self.get_or_create_nation(nation_name)
                    league_id = self.get_or_create_league(league_name, nation_name)
                    club_id = self.get_or_create_club(club_name, league_name, nation_name)

                    # Crea giocatore usando indici fissi
                    player = Player(
                        id=self.player_id_counter,
                        name=player_name,
                        nation_id=nation_id,
                        club_id=club_id,
                        position=position,
                        alternative_positions=self.clean_text(parts[5]) if len(parts) > 5 else "",
                        age=self.safe_int(parts[8]) if len(parts) > 8 else 0,  # age è spostato
                        overall=self.safe_int(parts[9]) if len(parts) > 9 else 0,  # overall è spostato
                        pace=self.safe_int(parts[10]) if len(parts) > 10 else 0,
                        shooting=self.safe_int(parts[11]) if len(parts) > 11 else 0,
                        passing=self.safe_int(parts[12]) if len(parts) > 12 else 0,
                        dribbling=self.safe_int(parts[13]) if len(parts) > 13 else 0,
                        defending=self.safe_int(parts[14]) if len(parts) > 14 else 0,
                        physical=self.safe_int(parts[15]) if len(parts) > 15 else 0,
                        height=self.clean_text(parts[43]) if len(parts) > 43 else "",
                        weight=self.clean_text(parts[44]) if len(parts) > 44 else "",
                        preferred_foot=self.clean_text(parts[47]) if len(parts) > 47 else "",
                        weak_foot=self.safe_int(parts[46]) if len(parts) > 46 else 0,
                        skill_moves=self.safe_int(parts[50]) if len(parts) > 50 else 0,
                        gk_diving=self.safe_int(parts[51]) if len(parts) > 51 and parts[51] else None,
                        gk_handling=self.safe_int(parts[52]) if len(parts) > 52 and parts[52] else None,
                        gk_kicking=self.safe_int(parts[53]) if len(parts) > 53 and parts[53] else None,
                        gk_positioning=self.safe_int(parts[54]) if len(parts) > 54 and parts[54] else None,
                        gk_reflexes=self.safe_int(parts[55]) if len(parts) > 55 and parts[55] else None
                    )

                    # Filtra giocatori con dati validi (criteri più permissivi)
                    if player.overall >= 40 and player.age > 15 and player.age < 50:
                        self.players.append(player)
                        self.player_id_counter += 1

                        # Aggiorna contatori nelle entità
                        if club_name in self.clubs:
                            self.clubs[club_name].players_count += 1
                        if league_name in self.leagues:
                            self.leagues[league_name].teams_count = len([c for c in self.clubs.values() if c.league_id == league_id])

                except Exception as e:
                    print(f"Errore nel caricamento del giocatore alla riga {line_num}: {e}")
                    continue

        print(f"Caricati {len(self.players)} giocatori validi.")
        print(f"Create {len(self.nations)} nazioni.")
        print(f"Creati {len(self.leagues)} campionati.")
        print(f"Create {len(self.clubs)} squadre.")

    def save_to_dat_files(self, output_dir: str = "data") -> None:
        """Salva tutti i dati in file .dat separati"""
        # Crea directory se non esiste
        os.makedirs(output_dir, exist_ok=True)

        # Salva nazioni
        nations_data = [nation.to_dict() for nation in self.nations.values()]
        with open(os.path.join(output_dir, "nations.dat"), 'w', encoding='utf-8') as f:
            json.dump(nations_data, f, indent=2, ensure_ascii=False)

        # Salva campionati
        leagues_data = [league.to_dict() for league in self.leagues.values()]
        with open(os.path.join(output_dir, "leagues.dat"), 'w', encoding='utf-8') as f:
            json.dump(leagues_data, f, indent=2, ensure_ascii=False)

        # Salva squadre
        clubs_data = [club.to_dict() for club in self.clubs.values()]
        with open(os.path.join(output_dir, "clubs.dat"), 'w', encoding='utf-8') as f:
            json.dump(clubs_data, f, indent=2, ensure_ascii=False)

        # Salva giocatori
        players_data = [player.to_dict() for player in self.players]
        with open(os.path.join(output_dir, "players.dat"), 'w', encoding='utf-8') as f:
            json.dump(players_data, f, indent=2, ensure_ascii=False)

        print(f"\nDati salvati in {output_dir}/:")
        print(f"- nations.dat: {len(self.nations)} nazioni")
        print(f"- leagues.dat: {len(self.leagues)} campionati")
        print(f"- clubs.dat: {len(self.clubs)} squadre")
        print(f"- players.dat: {len(self.players)} giocatori")

    def print_statistics(self) -> None:
        """Stampa statistiche sui dati caricati"""
        print(f"\n=== STATISTICHE DATI ===")
        print(f"Nazioni: {len(self.nations)}")
        print(f"Campionati: {len(self.leagues)}")
        print(f"Squadre: {len(self.clubs)}")
        print(f"Giocatori: {len(self.players)}")

        # Top 10 campionati per numero di squadre
        leagues_by_teams = sorted(self.leagues.values(), key=lambda x: x.teams_count, reverse=True)
        print(f"\n=== TOP 10 CAMPIONATI (per squadre) ===")
        for league in leagues_by_teams[:10]:
            print(f"- {league.name}: {league.teams_count} squadre")

        # Top 10 squadre per numero di giocatori
        clubs_by_players = sorted(self.clubs.values(), key=lambda x: x.players_count, reverse=True)
        print(f"\n=== TOP 10 SQUADRE (per giocatori) ===")
        for club in clubs_by_players[:10]:
            print(f"- {club.name}: {club.players_count} giocatori")

        # Distribuzione per posizione
        position_counts = defaultdict(int)
        for player in self.players:
            position_counts[player.position] += 1

        print(f"\n=== DISTRIBUZIONE POSIZIONI ===")
        for position, count in sorted(position_counts.items()):
            print(f"- {position}: {count} giocatori")
                    


if __name__ == "__main__":
    # Carica e struttura i dati FIFA 24
    loader = DataLoader()
    loader.load_players_from_csv('male_fc_24_players.csv')
    loader.print_statistics()
    loader.save_to_dat_files()

    print(f"\n=== PROCESSO COMPLETATO ===")
    print("I dati sono stati strutturati e salvati nei file .dat")
    print("Puoi ora utilizzare questi file per il tuo manageriale di calcio!")
